﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>PreviewFramework.DevTools</id>
    <version>0.9.227-beta</version>
    <title>PreviewFramework DevTools</title>
    <authors><PERSON><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/BretJohnson/preview-framework</projectUrl>
    <description>A .NET global tool for launching PreviewFramework DevTools - a visual development environment for UI component previews</description>
    <releaseNotes>https://github.com/BretJohnson/ui-preview-framework.git/releases/tag/v0.9.227-beta</releaseNotes>
    <copyright>© Bret Johnson. All rights reserved.</copyright>
    <tags>preview ui components devtools maui wpf uno tool</tags>
    <packageTypes>
      <packageType name="DotnetTool" />
    </packageTypes>
    <repository type="git" url="https://github.com/BretJohnson/ui-preview-framework.git" branch="refs/heads/main" commit="63ee34fbc5c741957a753af75a9159658e6b5375" />
  </metadata>
</package>