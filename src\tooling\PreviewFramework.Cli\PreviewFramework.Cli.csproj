<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>preview-devtools</ToolCommandName>
  </PropertyGroup>

  <PropertyGroup>
    <PackageId>PreviewFramework.DevTools</PackageId>
    <Title>PreviewFramework DevTools</Title>
    <PackageDescription>A .NET global tool for launching PreviewFramework DevTools - a visual development environment for UI component previews</PackageDescription>
    <PackageTags>preview;ui;components;devtools;maui;wpf;uno</PackageTags>
  </PropertyGroup>

  <!-- Custom target to include DevTools output in the package -->
  <Target Name="IncludeDevToolsInPackage" BeforeTargets="GenerateNuspec">
    <!-- Build the DevTools project first -->
    <MSBuild Projects="../PreviewFramework.DevTools/PreviewFramework.DevTools.csproj"
             Targets="Build"
             Properties="Configuration=$(Configuration);TargetFramework=net9.0-desktop" />

    <!-- Include all DevTools output files -->
    <ItemGroup>
      <DevToolsFiles Include="../PreviewFramework.DevTools/bin/$(Configuration)/net9.0-desktop/**/*" />
      <Content Include="@(DevToolsFiles)"
               PackagePath="tools/devtools/%(RecursiveDir)%(Filename)%(Extension)"
               Pack="true" />
    </ItemGroup>
  </Target>

</Project>
